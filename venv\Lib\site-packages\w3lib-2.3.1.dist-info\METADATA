Metadata-Version: 2.2
Name: w3lib
Version: 2.3.1
Summary: Library of web-related functions
Home-page: https://github.com/scrapy/w3lib
Author: Scrapy project
Author-email: <EMAIL>
License: BSD
Project-URL: Documentation, https://w3lib.readthedocs.io/en/latest/
Project-URL: Source Code, https://github.com/scrapy/w3lib
Project-URL: Issue Tracker, https://github.com/scrapy/w3lib/issues
Platform: Any
Classifier: Development Status :: 5 - Production/Stable
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Internet :: WWW/HTTP
Requires-Python: >=3.9
Description-Content-Type: text/x-rst
License-File: LICENSE
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: license
Dynamic: platform
Dynamic: project-url
Dynamic: requires-python
Dynamic: summary

=====
w3lib
=====

.. image:: https://github.com/scrapy/w3lib/actions/workflows/tests.yml/badge.svg
   :target: https://github.com/scrapy/w3lib/actions

.. image:: https://img.shields.io/codecov/c/github/scrapy/w3lib/master.svg
   :target: http://codecov.io/github/scrapy/w3lib?branch=master
   :alt: Coverage report


Overview
========

This is a Python library of web-related functions, such as:

* remove comments, or tags from HTML snippets
* extract base url from HTML snippets
* translate entites on HTML strings
* convert raw HTTP headers to dicts and vice-versa
* construct HTTP auth header
* converting HTML pages to unicode
* sanitize urls (like browsers do)
* extract arguments from urls

Requirements
============

Python 3.9+

Install
=======

``pip install w3lib``

Documentation
=============

See http://w3lib.readthedocs.org/

License
=======

The w3lib library is licensed under the BSD license.
