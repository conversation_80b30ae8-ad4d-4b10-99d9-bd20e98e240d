import scrapy
from scrapy.spiders import Crawl<PERSON>pider, Rule
from scrapy.linkextractors import LinkExtractor
import sqlite3
from urllib.parse import urlparse
import re


class FaithSpider(CrawlSpider):
    name = 'faith_spider'
    allowed_domains = ['faithinfotechacademy.com']
    start_urls = [
        'https://www.faithinfotechacademy.com/',
        'https://faithinfotechacademy.com/freshers',
        'https://faithinfotechacademy.com/pythonmern',
        'https://faithinfotechacademy.com/pythonds',
        'https://faithinfotechacademy.com/java',
        'https://faithinfotechacademy.com/dotnet',
        'https://faithinfotechacademy.com/pythondevops',
        'https://faithinfotechacademy.com/infosec',
        'https://faithinfotechacademy.com/internpro',
        'https://faithinfotechacademy.com/corporate',
        'https://faithinfotechacademy.com/academic',
        'https://faithinfotechacademy.com/about',
        'https://faithinfotechacademy.com/enquire',
        'https://faithinfotechacademy.com/privacy',
        'https://faithinfotechacademy.com/internprostep1',
    ]

    section_map = {
        'https://www.faithinfotechacademy.com': 'Homepage',
        'https://faithinfotechacademy.com/freshers': 'Courses',
        'https://faithinfotechacademy.com/pythonmern': 'Python MERN Training',
        'https://faithinfotechacademy.com/pythonds': 'Python Data Science & AI',
        'https://faithinfotechacademy.com/java': 'Java Full Stack Training',
        'https://faithinfotechacademy.com/dotnet': '.NET Full Stack Training',
        'https://faithinfotechacademy.com/pythondevops': 'Python DevOps Training',
        'https://faithinfotechacademy.com/infosec': 'Information Security',
        'https://faithinfotechacademy.com/internpro': 'InternPro Program',
        'https://faithinfotechacademy.com/corporate': 'Corporate Services',
        'https://faithinfotechacademy.com/academic': 'Academic Collaboration',
        'https://faithinfotechacademy.com/about': 'About Us',
        'https://faithinfotechacademy.com/enquire': 'Course Enquiry',
        'https://faithinfotechacademy.com/privacy': 'Privacy Policy',
        'https://faithinfotechacademy.com/internprostep1': 'InternPro Application',
    }

    deny_patterns = [
        r'testimonials?', r'gallery', r'placement', r'blog', r'contact',
        r'facebook.com', r'youtube.com', r'linkedin.com',
        r'login', r'admin', r'\.pdf$', r'register'
    ]

    rules = (
        Rule(
            LinkExtractor(deny=deny_patterns),
            callback='parse_page',
            follow=True,
            process_links='log_links'
        ),
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.conn = sqlite3.connect('crawl_data.db')
        self.cursor = self.conn.cursor()
        self.cursor.execute('CREATE TABLE IF NOT EXISTS hashes (hash TEXT PRIMARY KEY)')
        self.conn.commit()
        self.visited_urls = set()
        self.seen_sections = set()

    def log_links(self, links):
        for link in links:
            self.logger.info(f"🔗 Following: {link.url}")
        return links

    def parse_page(self, response):
        if response.status != 200:
            self.logger.warning(f"❌ Non-200 response for {response.url}: {response.status}")
            return

        url = response.url.rstrip('/').lower()

        if url in self.visited_urls:
            self.logger.warning(f"⚠ Already visited: {url}")
            return
        self.visited_urls.add(url)

        canonical_url = response.xpath('//link[@rel="canonical"]/@href').get()
        if canonical_url:
            canonical_url = canonical_url.rstrip('/').lower()
            if canonical_url in self.visited_urls:
                self.logger.warning(f"⚠ Canonical already visited: {canonical_url}")
                return
            url = canonical_url
            self.visited_urls.add(url)

        section = self.section_map.get(url)
        if not section:
            path = urlparse(url).path.strip('/')
            section = path.split('/')[-1].replace('-', ' ').replace('_', ' ').title() if path else 'Homepage'

        if section in self.seen_sections:
            self.logger.warning(f"⚠ Duplicate section skipped: {section}")
            return
        self.seen_sections.add(section)

        # Focus on main content areas only
        content_blocks = response.xpath('''
            //main//p[not(ancestor::footer or ancestor::nav or ancestor::header)]//text() |
            //section[contains(@class, "content") or contains(@class, "main")]//p//text() |
            //div[contains(@class, "content") or contains(@class, "main-content")]//p//text() |
            //article//p//text()
        ''').getall()

        # Also get headings but only from content areas
        headings = response.xpath('''
            //main//h1//text() |
            //main//h2//text() |
            //main//h3//text() |
            //section[contains(@class, "content")]//h1//text() |
            //section[contains(@class, "content")]//h2//text() |
            //section[contains(@class, "content")]//h3//text() |
            //div[contains(@class, "content")]//h1//text() |
            //div[contains(@class, "content")]//h2//text() |
            //div[contains(@class, "content")]//h3//text()
        ''').getall()

        # Combine and clean text
        all_text = content_blocks + headings
        cleaned_sentences = []

        for text in all_text:
            text = text.strip()
            if text:
                # Remove unwanted phrases
                if "Corporate Training Talent Sourcing" in text or "Click on each Camp to view its details." in text:
                    continue
                    
                # Split into sentences
                sentences = re.split(r'(?<=[.!?])\s+', text)
                for sentence in sentences:
                    sentence = sentence.strip()
                    if sentence and len(sentence.split()) >= 4:  # Minimum 4 words
                        # Clean special characters
                        clean_sentence = re.sub(r'[^\w\s.,!?]', '', sentence)
                        clean_sentence = re.sub(r'\s+', ' ', clean_sentence).strip()
                        if clean_sentence:
                            # Capitalize and ensure punctuation
                            clean_sentence = clean_sentence[0].upper() + clean_sentence[1:]
                            if not clean_sentence[-1] in {'.', '!', '?'}:
                                clean_sentence += '.'
                            cleaned_sentences.append(clean_sentence)

        if not cleaned_sentences:
            self.logger.warning(f"⚠ No valid content found in {url}")
            return

        # Remove duplicates while preserving order
        seen_sentences = set()
        unique_sentences = []
        for sentence in cleaned_sentences:
            if sentence not in seen_sentences:
                seen_sentences.add(sentence)
                unique_sentences.append(sentence)

        self.logger.info(f"📦 Yielding section: {section} with {len(unique_sentences)} sentences")

        yield {
            "section": section,
            "text": unique_sentences
        }

    def closed(self, reason):
        self.conn.close()
        self.logger.info("🛑 Spider closed.")