import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import './Chatbot.css';

function Chatbot() {
  const [open, setOpen] = useState(false);
  const [messages, setMessages] = useState([
    { from: 'bot', text: 'Hi! How can I help you?' }
  ]);
  const [input, setInput] = useState('');
  const messagesEndRef = useRef(null);

  const handleSend = async () => {
    if (!input.trim()) return;

    const userInput = input.toLowerCase();
    setMessages(prev => [...prev, { from: 'user', text: input }]);
    setInput('');

    if (userInput === 'courses') {
      setMessages(prev => [...prev, { from: 'bot', text: 'Fetching courses...' }]);
      try {
        const response = await axios.get('http://127.0.0.1:8000/api/scraped/');
        const courses = response.data.map(course => course.title).join(', ');
        setMessages(prev => [
          ...prev,
          { from: 'bot', text: `Here are our available courses: ${courses}` }
        ]);
      } catch (error) {
        setMessages(prev => [
          ...prev,
          { from: 'bot', text: 'Failed to fetch courses. Please try again later.' }
        ]);
      }
    } else {
      setTimeout(() => {
        setMessages(prev => [...prev, { from: 'bot', text: 'This is a prompt response.' }]);
      }, 500);
    }
  };

  const handleRetrieval = () => {
    setMessages(prev => [...prev, { from: 'bot', text: 'Retrieval button clicked!' }]);
  };

  // Auto scroll to bottom on new message
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  return (
    <div>
      <button className="chatbot-icon-btn" onClick={() => setOpen(!open)}>
        <span role="img" aria-label="chat">💬</span>
      </button>
      {open && (
        <div className="chatbot-window">
          <div className="chatbot-header">
            Chatbot
            <button className="chatbot-close-btn" onClick={() => setOpen(false)}>×</button>
          </div>
          <div className="chatbot-messages">
            {messages.map((msg, idx) => (
              <div key={idx} className={`chatbot-msg ${msg.from}`}>
                {msg.text}
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>
          <div className="chatbot-input-row">
            <input
              value={input}
              onChange={e => setInput(e.target.value)}
              onKeyDown={e => e.key === 'Enter' && handleSend()}
              placeholder="Type your message..."
            />
            <button onClick={handleSend}>Send</button>
            <button className="retrieval-btn" onClick={handleRetrieval}>Retrieval</button>
          </div>
        </div>
      )}
    </div>
  );
}

export default Chatbot;
