from django.shortcuts import render

# Create your views here.
# faith_backend/faithapp/views.py
import torch
torch.set_num_threads(1)
import os
os.environ["TOKENIZERS_PARALLELISM"] = "false"


from rest_framework import generics
from rest_framework.views import APIView
from rest_framework.response import Response
from .models import ScrapedData
from .serializers import ScrapedDataSerializer
import subprocess
import os

# ------------------------ #
# 1. 📋 List/Create Scraped #
# ------------------------ #
class ScrapedDataListCreateView(generics.ListCreateAPIView):
    queryset = ScrapedData.objects.all()
    serializer_class = ScrapedDataSerializer

# ---------------------------- #
# 2. ❌ Delete All ScrapedData #
# ---------------------------- #
class ScrapedDataDeleteAllView(APIView):
    def delete(self, request):
        ScrapedData.objects.all().delete()
        return Response({"message": "✅ All data deleted."})

# ------------------------------ #
# 3. 🔁 Re-Scrape via Scrapy Bot #
# ------------------------------ #
class ReRetrieveAPIView(APIView):
    def delete(self, request, *args, **kwargs):
        ScrapedData.objects.all().delete()

        project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../'))
        scrapy_project_dir = os.path.join(project_root, 'faith_scraper')

        scrapy_command = ['scrapy', 'crawl', 'faith_spider']

        try:
            result = subprocess.run(scrapy_command, cwd=scrapy_project_dir, check=True, capture_output=True, text=True)
            return Response({"status": "✅ Scraping triggered successfully.", "log": result.stdout})
        except subprocess.CalledProcessError as e:
            return Response({"error": e.stderr}, status=500)



from rest_framework.decorators import api_view
from rest_framework.response import Response
from transformers import AutoTokenizer, AutoModelForSeq2SeqLM
from .retriever import retrieve_best_match
import torch

# Load model once
FLAN_TOKENIZER = AutoTokenizer.from_pretrained("google/flan-t5-small", cache_dir="./hf_cache")
FLAN_MODEL = AutoModelForSeq2SeqLM.from_pretrained("google/flan-t5-small", cache_dir="./hf_cache")
FLAN_MODEL.eval()

@api_view(['POST'])
def RAGAnswerAPIView(request):
    query = request.data.get("query", "").strip()
    if not query:
        return Response({"answer": "❌ No query provided."}, status=400)

    context = retrieve_best_match(query)
    if not context:
        return Response({"answer": "❌ I couldn't find anything relevant in our knowledge base."})

    prompt = f"""You are an expert assistant for Faith Infotech Academy. Use the context below to provide a detailed, helpful answer to the question.

Context:
{context}

Question: {query}

Guidelines for your answer:
1. Be specific and detailed
2. If the question is about courses or programs, mention durations, key features, and benefits
3. If the question is about admissions or fees, provide clear information
4. If you're not sure, say so but try to provide relevant information
5. Keep the answer professional but friendly

Answer:"""

    inputs = FLAN_TOKENIZER(prompt, return_tensors="pt", truncation=True, max_length=1024)
    
    try:
        with torch.no_grad():
            output = FLAN_MODEL.generate(
                **inputs,
                max_new_tokens=300,
                temperature=0.7,
                top_p=0.9,
                do_sample=True
            )
        
        answer = FLAN_TOKENIZER.decode(output[0], skip_special_tokens=True).strip()

        if not answer or len(answer) < 50:
            return Response({
                "answer": context[:1000]
            })

        return Response({"answer": answer})
    
    except Exception as e:
        return Response({
            "answer": "❌ An error occurred while generating the answer. Here's the relevant context I found:\n\n" + context[:1000]
        })