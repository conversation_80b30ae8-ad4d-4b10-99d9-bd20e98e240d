import sys
import os
import django
from django.db import IntegrityError

# Setup Django
sys.path.append('C:/Users/<USER>/Desktop/chatbot/chatp/faith_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'faith_backend.settings')
django.setup()

from faithapp.models import ScrapedData
from django.db import IntegrityError
import traceback
import threading

class FaithPipeline:
    def save_item(self, item):
        try:
            ScrapedData.objects.create(
                section=item['section'],
                text='\n'.join(item['text']),
            )
            print(f"✅ Successfully saved: {item['section']}")
        except IntegrityError:
            print(f"⚠ Duplicate skipped: {item['section']}")
        except Exception as e:
            print(f"❌ Exception while saving {item['section']}: {e}")
            traceback.print_exc()

    def process_item(self, item, spider):
        print("🚀 FaithPipeline is active")
        print("📥 Item received:", item)

        # Run save_item in a new thread to avoid async context issues
        threading.Thread(target=self.save_item, args=(item,)).start()

        return item