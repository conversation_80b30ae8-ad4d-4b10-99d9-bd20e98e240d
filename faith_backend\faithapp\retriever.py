import faiss
import numpy as np
import torch
import re
from transformers import AutoTokenizer, AutoModel
from .models import ScrapedData

# Embedding model
EMBED_TOKENIZER = AutoTokenizer.from_pretrained("BAAI/bge-small-en")
EMBED_MODEL = AutoModel.from_pretrained("BAAI/bge-small-en")
EMBED_MODEL.eval()

# Globals
index = None
chunk_data = None

def clean_text(text):
    """Clean and normalize text"""
    text = re.sub(r'\s+', ' ', text)  # Replace multiple spaces/newlines with single space
    text = text.strip()
    return text

def embed(texts):
    inputs = EMBED_TOKENIZER(texts, return_tensors="pt", padding=True, truncation=True, max_length=512)
    with torch.no_grad():
        outputs = EMBED_MODEL(**inputs)
        return outputs.last_hidden_state[:, 0, :].cpu().numpy()

def get_faiss_index():
    global index, chunk_data
    if index is not None:
        return index, chunk_data

    # Load from database and chunk
    all_chunks = []
    chunk_data = []

    for entry in ScrapedData.objects.all():
        section = entry.section
        text = clean_text(entry.text)
        
        # Split into paragraphs first
        paragraphs = [p.strip() for p in text.split('\n') if p.strip()]
        
        # Further split long paragraphs into sentences
        for para in paragraphs:
            if len(para) > 200:
                sentences = re.split(r'(?<!\w\.\w.)(?<![A-Z][a-z]\.)(?<=\.|\?)\s', para)
                current_chunk = []
                for sentence in sentences:
                    if len(sentence.split()) > 5:  # Ignore very short sentences
                        current_chunk.append(sentence)
                        if len(' '.join(current_chunk)) > 150:  # Make chunks of reasonable size
                            chunk_text = ' '.join(current_chunk)
                            all_chunks.append(chunk_text)
                            chunk_data.append({"section": section, "text": chunk_text})
                            current_chunk = []
                # Add remaining sentences
                if current_chunk:
                    chunk_text = ' '.join(current_chunk)
                    all_chunks.append(chunk_text)
                    chunk_data.append({"section": section, "text": chunk_text})
            else:
                all_chunks.append(para)
                chunk_data.append({"section": section, "text": para})

    if not all_chunks:
        return None, None

    vectors = embed(all_chunks).astype("float32")
    index = faiss.IndexFlatL2(vectors.shape[1])
    index.add(vectors)
    return index, chunk_data

def retrieve_best_match(query, top_k=5):
    try:
        index, chunk_data = get_faiss_index()
        if index is None or chunk_data is None:
            return None

        query_vector = embed([clean_text(query)]).astype("float32")
        distances, indices = index.search(query_vector, top_k)

        # Get unique chunks (avoid duplicates)
        seen_chunks = set()
        context_chunks = []
        
        for i, idx in enumerate(indices[0]):
            if idx < len(chunk_data) and idx >= 0:  # Check bounds
                chunk = chunk_data[idx]
                chunk_text = chunk["text"]
                if chunk_text not in seen_chunks:
                    seen_chunks.add(chunk_text)
                    try:
                        score = float(1 / (1 + distances[0][i])) if i < len(distances[0]) else 0.5
                    except:
                        score = 0.5
                    context_chunks.append({
                        "text": chunk_text,
                        "section": chunk["section"],
                        "score": score
                    })

        # Sort by relevance score
        context_chunks.sort(key=lambda x: x["score"], reverse=True)
        
        # Format the context with section information
        formatted_context = []
        for chunk in context_chunks[:2]:  # Take top 3 most relevant
            formatted_context.append(f"\n{chunk['text']}")
        
        return "\n\n".join(formatted_context) if formatted_context else None
    
    except Exception as e:
        print(f"Error in retrieve_best_match: {str(e)}")
        return None