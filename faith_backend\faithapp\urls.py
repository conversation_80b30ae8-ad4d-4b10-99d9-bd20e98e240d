# faith_backend/faithapp/urls.py

from django.urls import path
from .views import (
    ScrapedDataListCreateView,
    ScrapedDataDeleteAllView,
    ReRetrieveAPIView,RAGAnswerAPIView
)

urlpatterns = [
    path('scraped/', ScrapedDataListCreateView.as_view(), name='scraped-list-create'),
    path('scraped/delete_all/', ScrapedDataDeleteAllView.as_view(), name='scraped-delete-all'),
    path('retrieve-again/', ReRetrieveAPIView.as_view(), name='re-retrieve'),
    path('chatbot/', RAGAnswerAPIView, name='rag-answer-api'),

]